// Interface for products with image data
interface ProductWithImages {
  images?: string[];
  imageTypes?: string[];
  externalImages?: string[];
}

/**
 * Helper function to get the first available image from a product
 * Handles both internal and external images properly
 */
export function getFirstAvailableImage(
  product: ProductWithImages
): string | null {
  if (!product.images || product.images.length === 0) return null;

  for (let i = 0; i < product.images.length; i++) {
    const imageType = product.imageTypes?.[i] || "INTERNAL";
    const internalImage = product.images[i];
    const externalImage = product.externalImages?.[i];

    if (imageType === "INTERNAL" && internalImage) {
      return internalImage;
    } else if (imageType === "EXTERNAL" && externalImage) {
      return externalImage;
    }
  }

  return null;
}

/**
 * Helper function to get all available images from a product
 * Returns array of image URLs (both internal and external)
 */
export function getAllAvailableImages(product: ProductWithImages): string[] {
  if (!product.images || product.images.length === 0) return [];

  const availableImages: string[] = [];

  for (let i = 0; i < product.images.length; i++) {
    const imageType = product.imageTypes?.[i] || "INTERNAL";
    const internalImage = product.images[i];
    const externalImage = product.externalImages?.[i];

    if (imageType === "INTERNAL" && internalImage) {
      availableImages.push(internalImage);
    } else if (imageType === "EXTERNAL" && externalImage) {
      availableImages.push(externalImage);
    }
  }

  return availableImages;
}
